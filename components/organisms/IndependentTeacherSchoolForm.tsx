'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { createSchool } from '@/actions/school.action';
import { useRouter } from 'next/navigation';

// Define the form input interface
interface IndependentTeacherSchoolFormInput {
  name: string;
  address: string;
  phoneNumber: string;
  email: string;
  registeredNumber: string;
}

// Define Zod validation schema
const independentTeacherSchoolSchema = z.object({
  name: z.string().min(1, 'School name is required').max(255, 'School name is too long'),
  address: z.string().min(1, 'School address is required').max(500, 'Address is too long'),
  phoneNumber: z.string()
    .min(1, 'School phone is required')
    .regex(/^[\+]?[0-9\s\-\(\)]{7,20}$/, 'Please enter a valid phone number'),
  email: z.string()
    .min(1, 'School email is required')
    .email('Please enter a valid email address'),
  registeredNumber: z.string().min(1, 'Registered number is required'),
});

export const IndependentTeacherSchoolForm: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<IndependentTeacherSchoolFormInput>({
    resolver: zodResolver(independentTeacherSchoolSchema),
  });

  const onSubmit: SubmitHandler<IndependentTeacherSchoolFormInput> = async (data) => {
    setIsSubmitting(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      // Create FormData as expected by the createSchool server action
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('address', data.address);
      formData.append('phoneNumber', data.phoneNumber);
      formData.append('email', data.email);
      formData.append('registeredNumber', data.registeredNumber);

      // Call the server action with FormData
      const result = await createSchool(formData);

      if (result.status === 'success') {
        setSuccessMessage('School created successfully! Redirecting to your school dashboard...');
        reset(); // Reset the form on success
        
        // Redirect to My School page after a short delay
        setTimeout(() => {
          router.push('/my-school');
        }, 2000);
      } else {
        // Handle error response
        const errorMsg = Array.isArray(result.message)
          ? result.message.map((m: any) => 
              typeof m === 'object' && m.message ? m.message : String(m)
            ).join(', ')
          : String(result.message || 'Failed to create school');
        setErrorMessage(errorMsg);
      }
    } catch (error: any) {
      console.error('Error creating school:', error);
      setErrorMessage(error.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      {/* Success Alert */}
      {successMessage && (
        <div role="alert" className="alert alert-success mb-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 shrink-0 stroke-current"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{successMessage}</span>
        </div>
      )}

      {/* Error Alert */}
      {errorMessage && (
        <div role="alert" className="alert alert-error mb-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 shrink-0 stroke-current"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{errorMessage}</span>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="w-full">
        <fieldset className="border border-gray-300 rounded-lg p-6">
          <legend className="text-lg font-semibold text-gray-700 px-2">
            Create Your School
          </legend>

          <div className="space-y-4">
            {/* School Name Field */}
            <div className="form-control">
              <label htmlFor="name" className="label">
                <span className="label-text font-medium">School Name *</span>
              </label>
              <input
                id="name"
                type="text"
                placeholder="Enter school name"
                className={`input input-bordered w-full ${
                  errors.name ? 'input-error' : ''
                }`}
                {...register('name')}
                disabled={isSubmitting}
              />
              {errors.name && (
                <div className="label">
                  <span className="label-text-alt text-error">
                    {errors.name.message}
                  </span>
                </div>
              )}
            </div>

            {/* School Address Field */}
            <div className="form-control">
              <label htmlFor="address" className="label">
                <span className="label-text font-medium">School Address *</span>
              </label>
              <input
                id="address"
                type="text"
                placeholder="Enter school address"
                className={`input input-bordered w-full ${
                  errors.address ? 'input-error' : ''
                }`}
                {...register('address')}
                disabled={isSubmitting}
              />
              {errors.address && (
                <div className="label">
                  <span className="label-text-alt text-error">
                    {errors.address.message}
                  </span>
                </div>
              )}
            </div>

            {/* School Phone Field */}
            <div className="form-control">
              <label htmlFor="phoneNumber" className="label">
                <span className="label-text font-medium">School Phone *</span>
              </label>
              <input
                id="phoneNumber"
                type="tel"
                placeholder="Enter school phone number"
                className={`input input-bordered w-full ${
                  errors.phoneNumber ? 'input-error' : ''
                }`}
                {...register('phoneNumber')}
                disabled={isSubmitting}
              />
              {errors.phoneNumber && (
                <div className="label">
                  <span className="label-text-alt text-error">
                    {errors.phoneNumber.message}
                  </span>
                </div>
              )}
            </div>

            {/* School Email Field */}
            <div className="form-control">
              <label htmlFor="email" className="label">
                <span className="label-text font-medium">School Email *</span>
              </label>
              <input
                id="email"
                type="email"
                placeholder="Enter school email address"
                className={`input input-bordered w-full ${
                  errors.email ? 'input-error' : ''
                }`}
                {...register('email')}
                disabled={isSubmitting}
              />
              {errors.email && (
                <div className="label">
                  <span className="label-text-alt text-error">
                    {errors.email.message}
                  </span>
                </div>
              )}
            </div>

            {/* Registered Number Field */}
            <div className="form-control">
              <label htmlFor="registeredNumber" className="label">
                <span className="label-text font-medium">Registered Number *</span>
              </label>
              <input
                id="registeredNumber"
                type="text"
                placeholder="Enter school registered number"
                className={`input input-bordered w-full ${
                  errors.registeredNumber ? 'input-error' : ''
                }`}
                {...register('registeredNumber')}
                disabled={isSubmitting}
              />
              {errors.registeredNumber && (
                <div className="label">
                  <span className="label-text-alt text-error">
                    {errors.registeredNumber.message}
                  </span>
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="form-control mt-6">
              <button
                type="submit"
                className={`btn btn-primary w-full ${
                  isSubmitting ? 'loading' : ''
                }`}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Creating School...
                  </>
                ) : (
                  'Create School'
                )}
              </button>
            </div>
          </div>
        </fieldset>
      </form>
    </div>
  );
};

export default IndependentTeacherSchoolForm;
